"""
浏览器池管理器 - 优化浏览器实例的创建和复用
"""

import threading
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from contextlib import contextmanager
from pathlib import Path

from playwright.sync_api import Browser, Browser<PERSON>ontext, Page, sync_playwright
from utils.logger import logger


@dataclass
class BrowserInstance:
    """浏览器实例信息"""
    browser: Browser
    context: BrowserContext
    page: Page
    created_at: float
    last_used: float
    in_use: bool = False
    test_count: int = 0
    max_tests: int = 50  # 单个实例最大测试数量


@dataclass
class BrowserPoolStats:
    """浏览器池统计信息"""
    total_created: int = 0
    total_reused: int = 0
    total_destroyed: int = 0
    active_instances: int = 0
    peak_instances: int = 0
    average_reuse_rate: float = 0.0


class BrowserPool:
    """
    浏览器池管理器
    
    功能：
    1. 复用浏览器实例，减少启动开销
    2. 自动清理过期实例
    3. 限制最大实例数量
    4. 提供统计信息
    """
    
    def __init__(self, max_browsers: int = 3, max_idle_time: int = 300, 
                 cleanup_interval: int = 60):
        """
        初始化浏览器池
        
        Args:
            max_browsers: 最大浏览器实例数
            max_idle_time: 最大空闲时间（秒）
            cleanup_interval: 清理检查间隔（秒）
        """
        self.max_browsers = max_browsers
        self.max_idle_time = max_idle_time
        self.cleanup_interval = cleanup_interval
        
        self.instances: List[BrowserInstance] = []
        self.stats = BrowserPoolStats()
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._stop_cleanup = False
        
        # 启动清理线程
        self._start_cleanup_thread()
        
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while not self._stop_cleanup:
                try:
                    self._cleanup_expired_instances()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    logger.error(f"浏览器池清理线程错误: {e}")
                    
        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        logger.debug("浏览器池清理线程已启动")
        
    def get_browser_instance(self, browser_type: str = "chromium", 
                           browser_config: Dict[str, Any] = None) -> BrowserInstance:
        """
        获取浏览器实例
        
        Args:
            browser_type: 浏览器类型
            browser_config: 浏览器配置
            
        Returns:
            浏览器实例
        """
        with self._lock:
            # 尝试复用现有实例
            for instance in self.instances:
                if (not instance.in_use and 
                    instance.test_count < instance.max_tests and
                    time.time() - instance.last_used < self.max_idle_time):
                    
                    instance.in_use = True
                    instance.last_used = time.time()
                    instance.test_count += 1
                    self.stats.total_reused += 1
                    
                    logger.debug(f"复用浏览器实例，已使用 {instance.test_count} 次")
                    return instance
            
            # 如果没有可复用的实例且未达到最大数量，创建新实例
            if len(self.instances) < self.max_browsers:
                instance = self._create_new_instance(browser_type, browser_config)
                self.instances.append(instance)
                self.stats.total_created += 1
                self.stats.active_instances += 1
                self.stats.peak_instances = max(self.stats.peak_instances, 
                                              self.stats.active_instances)
                
                logger.debug(f"创建新浏览器实例，当前活跃实例数: {self.stats.active_instances}")
                return instance
            
            # 如果达到最大数量，等待或强制复用最旧的实例
            logger.warning("浏览器池已满，强制复用最旧的实例")
            oldest_instance = min(self.instances, key=lambda x: x.last_used)
            
            # 如果实例正在使用，等待一段时间
            if oldest_instance.in_use:
                logger.warning("等待浏览器实例释放...")
                time.sleep(1)
                
            oldest_instance.in_use = True
            oldest_instance.last_used = time.time()
            oldest_instance.test_count += 1
            self.stats.total_reused += 1
            
            return oldest_instance
    
    def _create_new_instance(self, browser_type: str, 
                           browser_config: Dict[str, Any] = None) -> BrowserInstance:
        """创建新的浏览器实例"""
        try:
            playwright = sync_playwright().start()
            browser = getattr(playwright, browser_type).launch(
                headless=browser_config.get("headless", True) if browser_config else True,
                **(browser_config or {})
            )
            
            context_options = browser_config.get("context_options", {}) if browser_config else {}
            context = browser.new_context(**context_options)
            page = context.new_page()
            
            current_time = time.time()
            instance = BrowserInstance(
                browser=browser,
                context=context,
                page=page,
                created_at=current_time,
                last_used=current_time,
                in_use=True,
                test_count=1
            )
            
            logger.debug("成功创建新浏览器实例")
            return instance
            
        except Exception as e:
            logger.error(f"创建浏览器实例失败: {e}")
            raise
    
    def release_browser_instance(self, instance: BrowserInstance):
        """释放浏览器实例"""
        with self._lock:
            if instance in self.instances:
                instance.in_use = False
                instance.last_used = time.time()
                logger.debug(f"释放浏览器实例，已使用 {instance.test_count} 次")
    
    def _cleanup_expired_instances(self):
        """清理过期的浏览器实例"""
        with self._lock:
            current_time = time.time()
            expired_instances = []
            
            for instance in self.instances[:]:  # 创建副本进行迭代
                # 检查是否过期或超过最大测试次数
                if (not instance.in_use and 
                    (current_time - instance.last_used > self.max_idle_time or
                     instance.test_count >= instance.max_tests)):
                    expired_instances.append(instance)
            
            # 清理过期实例
            for instance in expired_instances:
                try:
                    self._destroy_instance(instance)
                    self.instances.remove(instance)
                    self.stats.total_destroyed += 1
                    self.stats.active_instances -= 1
                    logger.debug(f"清理过期浏览器实例，剩余活跃实例: {self.stats.active_instances}")
                except Exception as e:
                    logger.error(f"清理浏览器实例失败: {e}")
    
    def _destroy_instance(self, instance: BrowserInstance):
        """销毁浏览器实例"""
        try:
            if instance.page and not instance.page.is_closed():
                instance.page.close()
            if instance.context:
                instance.context.close()
            if instance.browser:
                instance.browser.close()
        except Exception as e:
            logger.error(f"销毁浏览器实例时出错: {e}")
    
    @contextmanager
    def get_browser(self, browser_type: str = "chromium", 
                   browser_config: Dict[str, Any] = None):
        """
        上下文管理器方式获取浏览器
        
        Usage:
            with browser_pool.get_browser() as instance:
                # 使用 instance.page 进行操作
                pass
        """
        instance = self.get_browser_instance(browser_type, browser_config)
        try:
            yield instance
        finally:
            self.release_browser_instance(instance)
    
    def get_stats(self) -> BrowserPoolStats:
        """获取统计信息"""
        with self._lock:
            # 计算平均复用率
            if self.stats.total_created > 0:
                self.stats.average_reuse_rate = (
                    self.stats.total_reused / 
                    (self.stats.total_created + self.stats.total_reused) * 100
                )
            return self.stats
    
    def shutdown(self):
        """关闭浏览器池"""
        logger.info("正在关闭浏览器池...")
        
        # 停止清理线程
        self._stop_cleanup = True
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
        
        # 清理所有实例
        with self._lock:
            for instance in self.instances[:]:
                try:
                    self._destroy_instance(instance)
                except Exception as e:
                    logger.error(f"关闭浏览器实例失败: {e}")
            
            self.instances.clear()
            self.stats.active_instances = 0
        
        logger.info("浏览器池已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except Exception:
            pass


# 全局浏览器池实例
browser_pool = BrowserPool()
